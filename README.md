# Simple MCP Server Example

This is a very basic example of an MCP (Model Context Protocol) server implementation in Python.

## What is MCP?

MCP (Model Context Protocol) is a protocol that allows AI models to securely connect to data sources and tools. This example demonstrates the core concepts of building an MCP server.

## Files

- `simple_mcp_server.py` - The main MCP server implementation
- `test_client.py` - A simple test client to interact with the server
- `README.md` - This documentation

## Features

The simple MCP server provides three basic tools:

1. **read_file** - Read the contents of a file
2. **write_file** - Write content to a file  
3. **calculate** - Perform basic arithmetic calculations

## How to Run

### Running the Test Client

The easiest way to test the server is using the included test client:

```bash
python test_client.py
```

This will:
1. Start the MCP server
2. Initialize the connection
3. List available tools
4. Test each tool with sample data
5. Clean up and stop the server

### Manual Testing

You can also interact with the server manually using JSON-RPC over stdin/stdout:

1. Start the server:
```bash
python simple_mcp_server.py
```

2. Send JSON-RPC requests via stdin. For example:

**Initialize:**
```json
{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {}, "clientInfo": {"name": "test", "version": "1.0.0"}}}
```

**List tools:**
```json
{"jsonrpc": "2.0", "id": 2, "method": "tools/list"}
```

**Call calculator:**
```json
{"jsonrpc": "2.0", "id": 3, "method": "tools/call", "params": {"name": "calculate", "arguments": {"expression": "2 + 3"}}}
```

## Key MCP Concepts Demonstrated

1. **JSON-RPC Communication** - The server uses JSON-RPC 2.0 over stdin/stdout
2. **Tool Registration** - Tools are defined with schemas describing their inputs
3. **Request Handling** - The server handles different MCP methods (initialize, tools/list, tools/call)
4. **Error Handling** - Proper error responses for invalid requests
5. **Protocol Compliance** - Follows MCP protocol specifications

## Server Structure

The `SimpleMCPServer` class handles:

- **Tool Definition** - Each tool has a name, description, and input schema
- **Request Routing** - Different methods are routed to appropriate handlers
- **Tool Execution** - Tools are executed with provided arguments
- **Response Formatting** - Results are formatted according to MCP protocol

## Extending the Server

To add new tools:

1. Add the tool definition to `self.tools` in `__init__`
2. Add a handler method (e.g., `_my_new_tool`)
3. Add the tool call routing in `handle_request`

Example:
```python
# In __init__:
"my_tool": {
    "name": "my_tool",
    "description": "Description of what the tool does",
    "inputSchema": {
        "type": "object",
        "properties": {
            "param": {"type": "string", "description": "Parameter description"}
        },
        "required": ["param"]
    }
}

# Handler method:
async def _my_tool(self, param: str) -> str:
    return f"Tool result for: {param}"

# In handle_request tools/call section:
elif tool_name == "my_tool":
    result = await self._my_tool(arguments.get("param"))
```

## Requirements

- Python 3.7+
- No external dependencies (uses only standard library)

This example provides a foundation for understanding MCP server development and can be extended with more sophisticated tools and capabilities.
